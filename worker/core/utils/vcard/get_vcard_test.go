//go:build unit

package vcard

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestGetVCard(t *testing.T) {
	t.Run("should return error when contact is nil", func(t *testing.T) {
		result, err := GetVCard(nil)

		require.Error(t, err)
		require.Equal(t, "contact is nil", err.<PERSON>rror())
		require.Empty(t, result)
	})

	t.Run("should generate vcard with complete contact information", func(t *testing.T) {
		contact := &VCardContact{
			Name: &ContactName{
				FirstName: "João",
				LastName:  "<PERSON>",
			},
			Phones: []*ContactPhones{
				{
					Phone: "+55 (11) 91234-5678",
					Type:  "Mobile",
					WaId:  "5511912345678",
				},
			},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:<PERSON>;<PERSON>;;;\nFN:<PERSON>\nitem1.TEL;waid=5511912345678:5511912345678\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})

	t.Run("should generate vcard with only first name", func(t *testing.T) {
		contact := &VCardContact{
			Name: &ContactName{
				FirstName: "Maria",
				LastName:  "",
			},
			Phones: []*ContactPhones{
				{
					Phone: "11987654321",
					Type:  "Mobile",
					WaId:  "5511987654321",
				},
			},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:;Maria;;;\nFN:Maria \nitem1.TEL;waid=5511987654321:11987654321\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})

	t.Run("should generate vcard with only last name", func(t *testing.T) {
		contact := &VCardContact{
			Name: &ContactName{
				FirstName: "",
				LastName:  "Santos",
			},
			Phones: []*ContactPhones{
				{
					Phone: "21999888777",
					Type:  "Mobile",
					WaId:  "5521999888777",
				},
			},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:Santos;;;;\nFN: Santos\nitem1.TEL;waid=5521999888777:21999888777\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})

	t.Run("should generate vcard without name", func(t *testing.T) {
		contact := &VCardContact{
			Name: nil,
			Phones: []*ContactPhones{
				{
					Phone: "85988776655",
					Type:  "Mobile",
					WaId:  "5585988776655",
				},
			},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:;;;;\nFN: \nitem1.TEL;waid=5585988776655:85988776655\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})

	t.Run("should generate vcard without phones", func(t *testing.T) {
		contact := &VCardContact{
			Name: &ContactName{
				FirstName: "Pedro",
				LastName:  "Oliveira",
			},
			Phones: []*ContactPhones{},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:Oliveira;Pedro;;;\nFN:Pedro Oliveira\nitem1.TEL;waid=:\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})

	t.Run("should use phone when WaId is empty", func(t *testing.T) {
		contact := &VCardContact{
			Name: &ContactName{
				FirstName: "Ana",
				LastName:  "Costa",
			},
			Phones: []*ContactPhones{
				{
					Phone: "+55 (11) 98765-4321",
					Type:  "Mobile",
					WaId:  "",
				},
			},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:Costa;Ana;;;\nFN:Ana Costa\nitem1.TEL;waid=5511987654321:5511987654321\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})

	t.Run("should use WaId when phone is empty", func(t *testing.T) {
		contact := &VCardContact{
			Name: &ContactName{
				FirstName: "Carlos",
				LastName:  "Ferreira",
			},
			Phones: []*ContactPhones{
				{
					Phone: "",
					Type:  "Mobile",
					WaId:  "5511999887766",
				},
			},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:Ferreira;Carlos;;;\nFN:Carlos Ferreira\nitem1.TEL;waid=5511999887766:5511999887766\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})

	t.Run("should use only first phone when multiple phones exist", func(t *testing.T) {
		contact := &VCardContact{
			Name: &ContactName{
				FirstName: "Lucia",
				LastName:  "Mendes",
			},
			Phones: []*ContactPhones{
				{
					Phone: "11987654321",
					Type:  "Mobile",
					WaId:  "5511987654321",
				},
				{
					Phone: "11876543210",
					Type:  "Work",
					WaId:  "5511876543210",
				},
			},
		}

		result, err := GetVCard(contact)

		require.NoError(t, err)
		expected := "BEGIN:VCARD\nVERSION:3.0\nN:Mendes;Lucia;;;\nFN:Lucia Mendes\nitem1.TEL;waid=5511987654321:11987654321\nitem1.X-ABLabel:Mobile\nEND:VCARD"
		require.Equal(t, expected, result)
	})
}

func TestCleanNumber(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "should clean phone with parentheses and dashes",
			input:    "(11) 91234-5678",
			expected: "11912345678",
		},
		{
			name:     "should clean phone with plus sign and spaces",
			input:    "+55 21 98765-4321",
			expected: "5521987654321",
		},
		{
			name:     "should remove all non-numeric characters",
			input:    "abc123def456ghi",
			expected: "123456",
		},
		{
			name:     "should return empty string for empty input",
			input:    "",
			expected: "",
		},
		{
			name:     "should return empty string for input without numbers",
			input:    "abcdef",
			expected: "",
		},
		{
			name:     "should keep only numbers",
			input:    "000-111-222",
			expected: "000111222",
		},
		{
			name:     "should handle complex formatting",
			input:    "+55 (11) 9.1234-5678 ext 123",
			expected: "5511912345678123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := cleanNumber(tt.input)
			require.Equal(t, tt.expected, result)
		})
	}
}
