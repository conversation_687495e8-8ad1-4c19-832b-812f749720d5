//go:build unit

package vcard

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestGetContactFromVCard(t *testing.T) {
	t.Run("should return error when vcard is empty", func(t *testing.T) {
		result, err := GetContactFromVCard("")

		require.Error(t, err)
		require.Equal(t, "vcard is empty", err.<PERSON>rror())
		require.Nil(t, result)
	})

	t.Run("should parse complete vcard successfully", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:<PERSON>;<PERSON>;;;
FN:<PERSON> Silva
item1.TEL;waid=5511912345678:5511912345678
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "<PERSON>", result.Name.FirstName)
		require.Equal(t, "<PERSON>", result.Name.LastName)
		require.Len(t, result.Phones, 1)
		require.Equal(t, "5511912345678", result.Phones[0].WaId)
		require.Equal(t, "5511912345678", result.Phones[0].Phone)
		require.Equal(t, "Mobile", result.Phones[0].Type)
	})

	t.Run("should parse vcard with only first name", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:;Maria;;;
FN:Maria
item1.TEL;waid=5511987654321:11987654321
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "Maria", result.Name.FirstName)
		require.Equal(t, "", result.Name.LastName)
		require.Len(t, result.Phones, 1)
		require.Equal(t, "5511987654321", result.Phones[0].WaId)
		require.Equal(t, "11987654321", result.Phones[0].Phone)
	})

	t.Run("should parse vcard with only last name", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:Santos;;;;
FN:Santos
item1.TEL;waid=5521999888777:21999888777
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "", result.Name.FirstName)
		require.Equal(t, "Santos", result.Name.LastName)
		require.Len(t, result.Phones, 1)
		require.Equal(t, "5521999888777", result.Phones[0].WaId)
		require.Equal(t, "21999888777", result.Phones[0].Phone)
	})

	t.Run("should parse vcard without name but with phone", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
item1.TEL;waid=5585988776655:85988776655
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "", result.Name.FirstName)
		require.Equal(t, "", result.Name.LastName)
		require.Len(t, result.Phones, 1)
		require.Equal(t, "5585988776655", result.Phones[0].WaId)
		require.Equal(t, "85988776655", result.Phones[0].Phone)
	})

	t.Run("should parse vcard with name but without phone", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:Oliveira;Pedro;;;
FN:Pedro Oliveira
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "Pedro", result.Name.FirstName)
		require.Equal(t, "Oliveira", result.Name.LastName)
		require.Len(t, result.Phones, 0)
	})

	t.Run("should handle vcard with extra whitespace", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:  Costa  ; Ana  ;;;
FN:Ana Costa
item1.TEL;waid=  5511987654321  :  5511987654321  
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "Ana", result.Name.FirstName)
		require.Equal(t, "Costa", result.Name.LastName)
		require.Len(t, result.Phones, 1)
		require.Equal(t, "5511987654321", result.Phones[0].WaId)
		require.Equal(t, "5511987654321", result.Phones[0].Phone)
	})

	t.Run("should handle vcard with incomplete N field", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:Silva
FN:Silva
item1.TEL;waid=5511999887766:5511999887766
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		// When N field has only one part (no semicolon), the function doesn't process it
		// So both FirstName and LastName remain empty
		require.Equal(t, "", result.Name.FirstName)
		require.Equal(t, "", result.Name.LastName)
		require.Len(t, result.Phones, 1)
	})

	t.Run("should handle vcard with malformed phone line", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:Ferreira;Carlos;;;
FN:Carlos Ferreira
item1.TEL;waid=5511999887766
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "Carlos", result.Name.FirstName)
		require.Equal(t, "Ferreira", result.Name.LastName)
		require.Len(t, result.Phones, 0) // Phone line is malformed, so no phone is added
	})

	t.Run("should parse multiple phone lines", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:Mendes;Lucia;;;
FN:Lucia Mendes
item1.TEL;waid=5511987654321:11987654321
item1.X-ABLabel:Mobile
item1.TEL;waid=5511876543210:11876543210
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "Lucia", result.Name.FirstName)
		require.Equal(t, "Mendes", result.Name.LastName)
		require.Len(t, result.Phones, 2)
		require.Equal(t, "5511987654321", result.Phones[0].WaId)
		require.Equal(t, "11987654321", result.Phones[0].Phone)
		require.Equal(t, "5511876543210", result.Phones[1].WaId)
		require.Equal(t, "11876543210", result.Phones[1].Phone)
	})

	t.Run("should ignore FN line as specified in code", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:Silva;João;;;
FN:Different Name
item1.TEL;waid=5511912345678:5511912345678
item1.X-ABLabel:Mobile
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		// Should use N field, not FN field
		require.Equal(t, "João", result.Name.FirstName)
		require.Equal(t, "Silva", result.Name.LastName)
	})

	t.Run("should return error for completely invalid vcard", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.Error(t, err)
		require.Equal(t, "vcard content is invalid or incomplete", err.Error())
		require.Nil(t, result)
	})

	t.Run("should return error for vcard with only empty fields", func(t *testing.T) {
		vcard := `BEGIN:VCARD
VERSION:3.0
N:;;;;
FN:
END:VCARD`

		result, err := GetContactFromVCard(vcard)

		require.Error(t, err)
		require.Equal(t, "vcard content is invalid or incomplete", err.Error())
		require.Nil(t, result)
	})

	t.Run("should handle vcard with different line endings", func(t *testing.T) {
		vcard := "BEGIN:VCARD\nVERSION:3.0\nN:Silva;João;;;\nFN:João Silva\nitem1.TEL;waid=5511912345678:5511912345678\nitem1.X-ABLabel:Mobile\nEND:VCARD"

		result, err := GetContactFromVCard(vcard)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Name)
		require.Equal(t, "João", result.Name.FirstName)
		require.Equal(t, "Silva", result.Name.LastName)
		require.Len(t, result.Phones, 1)
	})
}
