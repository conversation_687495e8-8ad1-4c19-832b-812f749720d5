package base_waba_adapter

import (
	"bytes"
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/storage"
	"digisac-go/worker/core/utils/common"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/goforj/godump"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	"golang.org/x/sync/singleflight"
)

type BaseWabaAdapterInterface interface {
	GetMessageApiUrl(ctx context.Context, service *models.Service) (string, error)
	GetTemplatesApiUrl(ctx context.Context, service *models.Service) (string, error)
	DeleteTemplateApiUrl(ctx context.Context, service *models.Service, templateName string) (string, error)
	DeleteTemplateApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error)
	GetMessageApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error)
	GetAuthenticationText(ctx context.Context, language string) string
	GetExpirationWarningText(ctx context.Context, language string) string
	ProcessAuthenticationTemplate(ctx context.Context, template *models.WhatsappBusinessTemplate, components []*models.WhatsappBusinessComponent) []*models.WhatsappBusinessComponent
	GetMediaExampleUrl(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (string, error)
}

type BaseWabaAdapter struct {
	BaseWabaAdapterInterface
	ServiceRepository  repositories.ServiceRepository
	templateRepository repositories.WhatsappBusinessTemplateRepository
	fileRepository     repositories.FileRepository
	storageService     *storage.StorageService
	Clients            map[uuid.UUID]*http.Client
	group              singleflight.Group
	Config             *config.Config
	MessageApiUrl      string
	MessageApiHeaders  map[string]string
}

func NewBaseWabaAdapter(
	serviceRepository repositories.ServiceRepository,
	templateRepository repositories.WhatsappBusinessTemplateRepository,
	fileRepository repositories.FileRepository,
	storageService *storage.StorageService,
	config *config.Config,
	BaseWabaAdapterInterface BaseWabaAdapterInterface,
) *BaseWabaAdapter {
	return &BaseWabaAdapter{
		// Precisa passar aqui o adapter pois é uma dependencia da interface apenas do BaseWabaAdapter
		BaseWabaAdapterInterface: BaseWabaAdapterInterface,
		templateRepository:       templateRepository,
		ServiceRepository:        serviceRepository,
		fileRepository:           fileRepository,
		storageService:           storageService,
		Clients:                  make(map[uuid.UUID]*http.Client),
		group:                    singleflight.Group{},
		Config:                   config,
	}
}

// MakeClient cria um novo cliente HTTP para o serviço
func (a *BaseWabaAdapter) MakeClient(ctx context.Context, serviceId uuid.UUID) (any, error) {
	slog.InfoContext(ctx, "MakeClient called", slog.String("serviceId", serviceId.String()))

	client := http.Client{
		Timeout: 2 * time.Minute,
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	a.MessageApiUrl, err = a.GetMessageApiUrl(ctx, service)

	if err != nil {
		return nil, fmt.Errorf("failed to set message api url: %w", err)
	}

	a.MessageApiHeaders, err = a.GetMessageApiHeaders(ctx, service)

	if err != nil {
		return nil, fmt.Errorf("failed to set message api headers: %w", err)
	}

	return &client, nil
}

// getClient obtém um cliente HTTP para o serviço
func (a *BaseWabaAdapter) GetClient(ctx context.Context, serviceId uuid.UUID) (*http.Client, error) {
	// singleflight.Group garante que apenas uma execução aconteça ao mesmo tempo para o mesmo serviceId
	client, err, _ := a.group.Do(serviceId.String(), func() (any, error) {
		if _, ok := a.Clients[serviceId]; !ok {
			client, err := a.MakeClient(ctx, serviceId)
			if err != nil {
				return nil, err
			}

			a.Clients[serviceId] = client.(*http.Client)
		}

		return a.Clients[serviceId], nil
	})

	if err != nil {
		slog.ErrorContext(ctx, "Error obtaining client from group", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to obtain client for serviceId %s: %w", serviceId, err)
	}

	return client.(*http.Client), nil
}

func (a *BaseWabaAdapter) Start(ctx context.Context, serviceId uuid.UUID) (err error) {
	_, err = a.GetClient(ctx, serviceId)

	if err != nil {
		slog.ErrorContext(ctx, "Error starting Whatsapp Business adapter", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to start adapter for serviceId %s: %w", serviceId, err)
	}

	return nil
}

func (a *BaseWabaAdapter) Shutdown(ctx context.Context, serviceId uuid.UUID) error {
	if a.Clients[serviceId] != nil {
		delete(a.Clients, serviceId)
	}
	return nil
}

func (a *BaseWabaAdapter) Refresh(ctx context.Context, serviceId uuid.UUID) error      { return nil }
func (a *BaseWabaAdapter) NewToken(ctx context.Context, serviceId uuid.UUID) error     { return nil }
func (a *BaseWabaAdapter) Takeover(ctx context.Context, serviceId uuid.UUID) error     { return nil }
func (a *BaseWabaAdapter) Logout(ctx context.Context, serviceId uuid.UUID) (err error) { return nil }

func (a *BaseWabaAdapter) SendMessageBase(ctx context.Context, serviceId uuid.UUID, payload []byte) (response *adapter_types.SendMessageResponse, err error) {
	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	req, err := http.NewRequest("POST", a.MessageApiUrl, bytes.NewBuffer(payload))

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	for key, value := range a.MessageApiHeaders {
		req.Header.Add(key, value)
	}

	res, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return &adapter_types.SendMessageResponse{
			Error: &models.MessageError{
				Code:          res.StatusCode,
				OriginalError: string(body),
			},
			Ack: "error",
		}, nil
	}

	var sendMessageResponse *adapter_types.WabaSendMessageResponse

	err = json.Unmarshal(body, &sendMessageResponse)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	timestamp := time.Now()

	return &adapter_types.SendMessageResponse{
		MessageId: sendMessageResponse.Messages[0].Id,
		Timestamp: &timestamp,
		Ack:       "0",
	}, nil

}

func (a *BaseWabaAdapter) SendText(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendTextPayload) (*adapter_types.SendMessageResponse, error) {
	data := &adapter_types.WabaSendTextPayload{

		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "text",
		Text: &adapter_types.TextPayload{
			Body: payload.Text,
		},
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	jsonData, _ := json.Marshal(data)

	return a.SendMessageBase(ctx, serviceId, jsonData)
}

func (a *BaseWabaAdapter) SendAudio(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendAudioPayload) (response *adapter_types.SendMessageResponse, err error) {
	data := &adapter_types.WabaSendAudioPayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "audio",
		Audio: &adapter_types.AudioPayload{
			Link: payload.Url,
		},
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	jsonData, _ := json.Marshal(data)

	return a.SendMessageBase(ctx, serviceId, jsonData)
}
func (a *BaseWabaAdapter) SendImage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendImagePayload) (response *adapter_types.SendMessageResponse, err error) {
	data := &adapter_types.WabaSendImagePayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "image",
		Image: &adapter_types.ImagePayload{
			Link:    payload.Url,
			Caption: payload.Caption,
		},
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	jsonData, _ := json.Marshal(data)

	return a.SendMessageBase(ctx, serviceId, jsonData)
}
func (a *BaseWabaAdapter) SendVideo(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendVideoPayload) (response *adapter_types.SendMessageResponse, err error) {
	data := &adapter_types.WabaSendVideoPayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "video",
		Video: &adapter_types.VideoPayload{
			Link:    payload.Url,
			Caption: payload.Caption,
		},
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	jsonData, _ := json.Marshal(data)

	return a.SendMessageBase(ctx, serviceId, jsonData)
}

func (a *BaseWabaAdapter) SendDocument(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendDocumentPayload) (response *adapter_types.SendMessageResponse, err error) {
	data := &adapter_types.WabaSendDocumentPayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "document",
		Document: &adapter_types.DocumentPayload{
			Link:     payload.Url,
			Caption:  payload.Caption,
			Filename: payload.Filename,
		},
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	jsonData, _ := json.Marshal(data)

	return a.SendMessageBase(ctx, serviceId, jsonData)
}

func (a *BaseWabaAdapter) SendTemplate(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendTemplatePayload) (*adapter_types.SendMessageResponse, error) {
	// https://partner-docs.gupshup.io/reference/post_partner-app-appid-v3-message-14
	data := &adapter_types.WabaSendTemplatePayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "template",
		Template: &adapter_types.TemplatePayload{
			Name:     payload.Template.Name,
			Language: &adapter_types.TemplateLanguage{Code: payload.Template.Language},
		},
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	if len(payload.Parameters) > 0 {
		componentsByType := make(map[string]*adapter_types.TemplateComponent)
		for _, parameter := range payload.Parameters {
			if _, exists := componentsByType[parameter.Type]; !exists {
				componentsByType[parameter.Type] = &adapter_types.TemplateComponent{
					Type:       parameter.Type,
					Parameters: []*adapter_types.TemplateParameter{},
				}
			}
		}

		for i := range payload.Parameters {
			component := componentsByType[payload.Parameters[i].Type]

			for j := range payload.Parameters[i].Parameters {
				param := payload.Parameters[i].Parameters[j]

				templateParam := &adapter_types.TemplateParameter{
					Type: param.Type,
					Text: param.Text,
				}

				if payload.Parameters[i].Type == "button" {
					var buttonType string // URL, PHONE_NUMBER

					for _, templateComponent := range payload.Template.Components {
						if templateComponent.Type == "BUTTONS" {
							buttonType = string(templateComponent.Buttons[0].Type)
							break
						}
					}

					if buttonType != "" {
						component.Index = "0" // Quando tem buttonType só enviamos 1 botão por isso o índice é sempre 0
						component.SubType = strings.ToLower(buttonType)
					}
				}

				if payload.Url != "" && component.Type == "header" {
					if param.Type == "image" {
						templateParam.Image = &adapter_types.TemplateImage{Link: payload.Url}
					}
					if param.Type == "document" {
						templateParam.Document = &adapter_types.TemplateDocument{Link: payload.Url}
					}
					if param.Type == "video" {
						templateParam.Video = &adapter_types.TemplateVideo{Link: payload.Url}
					}
					if param.Type == "audio" {
						templateParam.Audio = &adapter_types.TemplateAudio{Link: payload.Url}
					}
				}

				component.Parameters = append(component.Parameters, templateParam)
			}
		}

		// Adicionar os componentes na ordem correta
		for _, componentType := range []string{"header", "body", "footer", "button"} {
			if component, exists := componentsByType[componentType]; exists {
				data.Template.Components = append(data.Template.Components, component)
			}
		}
	}

	godump.Dump(data)

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal template data: %w", err)
	}

	slog.DebugContext(ctx, "Sending template payload", slog.String("jsonData", string(jsonData)))
	return a.SendMessageBase(ctx, serviceId, jsonData)
}

func (a *BaseWabaAdapter) SendInteractiveMessage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendInteractiveMessagePayload) (*adapter_types.SendMessageResponse, error) {
	godump.Dump(payload)

	data := &adapter_types.WabaSendInteractivePayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "interactive",
		Interactive:      payload.Interactive,
	}

	if payload.Url != "" {
		if data.Interactive.Header.Type == "image" {
			data.Interactive.Header.Image.Link = payload.Url
		}
		if data.Interactive.Header.Type == "video" {
			data.Interactive.Header.Video.Link = payload.Url
		}
		if data.Interactive.Header.Type == "document" {
			data.Interactive.Header.Document.Link = payload.Url
		}
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal interactive message data: %w", err)
	}

	godump.Dump(data)

	return a.SendMessageBase(ctx, serviceId, jsonData)
}

func (a *BaseWabaAdapter) SendSticker(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendStickerPayload) (*adapter_types.SendMessageResponse, error) {
	slog.InfoContext(ctx, "SendSticker called", slog.String("serviceId", serviceId.String()), slog.String("to", payload.To))

	data := &adapter_types.WabaSendStickerPayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "sticker",
		Sticker: &adapter_types.StickerPayload{
			Link: payload.Url,
		},
	}

	if payload.ReplyMessageId != "" {
		data.Context = &adapter_types.Context{
			MessageId: payload.ReplyMessageId,
		}
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal revoke reaction data: %w", err)
	}

	return a.SendMessageBase(ctx, serviceId, jsonData)
}

func (a *BaseWabaAdapter) SendReaction(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendReactionPayload) (response bool, err error) {
	slog.InfoContext(ctx, "SendReaction called", slog.String("serviceId", serviceId.String()), slog.String("to", payload.To), slog.String("messageId", payload.MessageId), slog.String("reaction", payload.Reaction))

	data := &adapter_types.WabaSendReactionPayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "reaction",
		Reaction: &adapter_types.ReactionPayload{
			MessageId: payload.MessageId,
			Emoji:     payload.Reaction,
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return false, fmt.Errorf("failed to marshal reaction data: %w", err)
	}

	slog.DebugContext(ctx, "Sending reaction payload", slog.String("jsonData", string(jsonData)))

	sendResponse, err := a.SendMessageBase(ctx, serviceId, jsonData)
	if err != nil {
		return false, fmt.Errorf("failed to send reaction: %w", err)
	}

	if sendResponse.Error != nil {
		slog.ErrorContext(ctx, "Error sending reaction", slog.String("serviceId", serviceId.String()), slog.Any("error", sendResponse.Error))
		return false, fmt.Errorf("error sending reaction: %s", sendResponse.Error.OriginalError)
	}

	return true, nil
}

func (a *BaseWabaAdapter) RevokeReaction(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.RevokeReactionPayload) (response bool, err error) {
	slog.InfoContext(ctx, "RevokeReaction called", slog.String("serviceId", serviceId.String()), slog.String("to", payload.To), slog.String("messageId", payload.MessageId))

	data := &adapter_types.WabaSendReactionPayload{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               payload.To,
		Type:             "reaction",
		Reaction: &adapter_types.ReactionPayload{
			MessageId: payload.MessageId,
			Emoji:     "",
		},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return false, fmt.Errorf("failed to marshal revoke reaction data: %w", err)
	}

	sendResponse, err := a.SendMessageBase(ctx, serviceId, jsonData)
	if err != nil {
		return false, fmt.Errorf("failed to revoke reaction: %w", err)
	}

	if sendResponse.Error != nil {
		slog.ErrorContext(ctx, "Error revoking reaction", slog.String("serviceId", serviceId.String()), slog.Any("error", sendResponse.Error))
		return false, fmt.Errorf("error revoking reaction: %s", sendResponse.Error.OriginalError)
	}

	return true, nil
}

func (a *BaseWabaAdapter) LoadEarlierMessages(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.LoadEarlierMessagesPayload) ([]*adapter_types.BuiltWebhook, error) {
	return nil, nil
}
func (a *BaseWabaAdapter) ForwardMessage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.ForwardMessagePayload) (*adapter_types.ForwardMessageResponse, error) {
	return nil, nil
}

func (a *BaseWabaAdapter) DownloadMedia(ctx context.Context, serviceId uuid.UUID, mediaId string, mediaUrl string) (*adapter_types.DownloadMediaResponse, error) {
	resp, err := http.Get(mediaUrl)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to download media", slog.String("mediaUrl", mediaUrl), slog.Any("error", err))
		return nil, fmt.Errorf("failed to download media from url %s: %w", mediaUrl, err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to read media response body", slog.String("mediaUrl", mediaUrl), slog.Any("error", err))
		return nil, fmt.Errorf("failed to read media data from url %s: %w", mediaUrl, err)
	}

	response := &adapter_types.DownloadMediaResponse{
		Type:   "stream",
		Stream: bytes.NewReader(data),
	}

	return response, nil
}

func (a *BaseWabaAdapter) BuildWebhook(ctx context.Context, serviceId uuid.UUID, payload any) ([]*adapter_types.BuiltWebhook, error) {
	godump.Dump(payload)

	var p *adapter_types.WhatsappBusinessWebhookPayload

	err := common.ToStruct(ctx, payload, &p)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to convert payload to WhatsappBusinessWebhookPayload", slog.Any("payload", payload), slog.Any("error", err))
		return nil, fmt.Errorf("failed to convert payload: %w", err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service by Id", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service by Id %s: %w", serviceId, err)
	}

	var builtWebhooks []*adapter_types.BuiltWebhook

	for _, entry := range p.Payload.Entry {
		for _, change := range entry.Changes {
			if change.Field != "messages" {
				continue
			}

			// Mensagens
			if len(change.Value.Messages) > 0 && len(change.Value.Contacts) > 0 {
				builtMessages, err := a.BuildMessages(service, change)

				if err != nil {
					return nil, fmt.Errorf("failed to build messages: %w", err)
				}

				builtWebhooks = append(builtWebhooks, builtMessages...)

				continue
			}

			// Status
			if len(change.Value.Statuses) > 0 {
				builtStatuses, err := a.BuildStatuses(service, change)

				if err != nil {
					return nil, fmt.Errorf("failed to build statuses: %w", err)
				}

				builtWebhooks = append(builtWebhooks, builtStatuses...)

				continue
			}

		}
	}

	if len(builtWebhooks) == 0 {
		slog.DebugContext(ctx, "No built webhooks", slog.String("serviceId", serviceId.String()), slog.Any("payload", payload))
	}

	godump.Dump(builtWebhooks)

	return builtWebhooks, nil
}

func (a *BaseWabaAdapter) BuildMessages(service *models.Service, change *adapter_types.WhatsappBusinessWebhookChange) ([]*adapter_types.BuiltWebhook, error) {
	var builtWebhooks []*adapter_types.BuiltWebhook

	for _, contact := range change.Value.Contacts {
		if contact.WaId == "" {
			return nil, fmt.Errorf("contact Id is empty")
		}

		webhookContact := &adapter_types.WebhookContact{
			Id:   contact.WaId,
			Name: contact.Profile.Name,
		}

		for _, message := range change.Value.Messages {
			messageType, err := a.getWebhookMessageType(message)

			if err != nil {
				return nil, fmt.Errorf("failed to get webhook message type: %w", err)
			}

			timestampDate := time.Now()

			parsedTime, err := cast.ToStringE(message.Timestamp)

			if err != nil {
				return nil, fmt.Errorf("failed to parse message timestamp: %w", err)
			}

			if parsedTime != "" {
				var milli int64

				if len(parsedTime) == 13 {
					milli = cast.ToInt64(parsedTime[10:13])
				}

				timestampDate = time.Unix(cast.ToInt64(parsedTime[:10]), milli)
			}

			webhookMessage := &adapter_types.WebhookMessage{
				Id:         message.Id,
				Type:       string(messageType),
				Timestamp:  &timestampDate,
				IsFromMe:   false,
				WhatsappId: "", // Usado apenas na gupshup
			}

			if message.Context != nil {
				replyMessageId := message.Context.Id

				if message.Context.GsId != "" {
					replyMessageId = message.Context.GsId
				}

				webhookMessage.ReplyMessageId = replyMessageId
			}

			switch messageType {
			case adapter_types.MESSAGE_TYPE_TEXT:
				if message.Button != nil { // Tratamos botões e mensagens interativas como texto, geralmente vem da resposta
					webhookMessage.Text = message.Button.Text
				} else if message.Interactive != nil {
					if message.Interactive.Type == "button_reply" {
						webhookMessage.Text = message.Interactive.ButtonReply.Title
					}
					if message.Interactive.Type == "list_reply" {
						webhookMessage.Text = message.Interactive.ListReply.Title
					}
				} else {
					webhookMessage.Text = message.Text.Body
				}
			case adapter_types.MESSAGE_TYPE_IMAGE:
				webhookMessage.File = &adapter_types.WebhookMessageFile{
					Id:       message.Image.Id,
					Mimetype: message.Image.MimeType,
					Url:      message.Image.URL,
				}
				webhookMessage.Text = message.Image.Caption
			case adapter_types.MESSAGE_TYPE_VIDEO:
				webhookMessage.File = &adapter_types.WebhookMessageFile{
					Id:       message.Video.Id,
					Mimetype: message.Video.MimeType,
					Url:      message.Video.URL,
				}
				webhookMessage.Text = message.Video.Caption
			case adapter_types.MESSAGE_TYPE_VOICE:
				webhookMessage.File = &adapter_types.WebhookMessageFile{
					Id:       message.Audio.Id,
					Mimetype: message.Audio.MimeType,
					Url:      message.Audio.URL,
				}
			case adapter_types.MESSAGE_TYPE_DOCUMENT:
				webhookMessage.File = &adapter_types.WebhookMessageFile{
					Id:       message.Document.Id,
					Mimetype: message.Document.MimeType,
					Url:      message.Document.URL,
					Name:     message.Document.FileName,
				}
				webhookMessage.Text = message.Document.Caption
			case adapter_types.MESSAGE_TYPE_STICKER:
				webhookMessage.File = &adapter_types.WebhookMessageFile{
					Id:       message.Sticker.Id,
					Mimetype: message.Sticker.MimeType,
					Url:      message.Sticker.URL,
				}
			case adapter_types.MESSAGE_TYPE_REACTION:
				webhookMessage.Id = message.Reaction.MessageId
				webhookMessage.WhatsappId = message.Reaction.MetaMsgId // Usado apenas na gupshup
				builtWebhooks = append(builtWebhooks, &adapter_types.BuiltWebhook{
					Contact: webhookContact,
					From:    webhookContact,
					Message: webhookMessage,
					Reaction: &adapter_types.WebhookReaction{
						NewReactions: []*adapter_types.Reaction{{Emoji: message.Reaction.Emoji}},
						Timestamp:    webhookMessage.Timestamp,
						IsFromMe:     false,
					},
				})
				continue
			case adapter_types.MESSAGE_TYPE_LOCATION:
				webhookMessage.Location = &adapter_types.WebhookMessageLocation{
					Lat: message.Location.Latitude,
					Lng: message.Location.Longitude,
				}
			case adapter_types.MESSAGE_TYPE_VCARD:
				for _, contact := range message.Contacts {
					vcard, err := a.getVcard(contact)

					if err != nil {
						return nil, fmt.Errorf("failed to get vcard: %w", err)
					}

					webhookMessage.VCard = append(webhookMessage.VCard, &models.MessageDataVCard{VCard: vcard})
				}
			}

			builtWebhooks = append(builtWebhooks, &adapter_types.BuiltWebhook{
				Contact: webhookContact,
				From:    webhookContact,
				Message: webhookMessage,
			})
		}
	}

	return builtWebhooks, nil
}

func (a *BaseWabaAdapter) BuildStatuses(service *models.Service, change *adapter_types.WhatsappBusinessWebhookChange) ([]*adapter_types.BuiltWebhook, error) {
	var builtWebhooks []*adapter_types.BuiltWebhook

	for _, status := range change.Value.Statuses {
		if status.Type == "set-callback" { // Usado para configurar o webhook
			return nil, nil
		}
		statusId := status.Id

		// Usado apenas na gupshup
		// Quando enviamos reação para o contato e ele reage, o id da mensagem da reação é o meta_msg_id não o gs_id.
		// O idFromService é o gs_id, dessa forma quando recebemos a reação não encontramos a mensagem pelo idFromService.
		// Por isso é salvo também o meta_msg_id em message.data.whatsappMessageId para termos a referencia e conseguirmos receber a reação.
		// Só temos esse meta_msg_id no status de sent, delivery e read, não temos na resposta do envio de mensagem
		// Tudo isso só acontece na gupshup, imagino que por terem o status "enqueued".
		// Solução migrada da digisac atual.
		// Pensei em alterar e fazer apenas um update no idFromService, mas não é correto, pois a gupshup trabalha com o gs_id.
		var whatsappId string

		// Gupshup usa GsId
		if status.GsId != "" {
			statusId = status.GsId
			whatsappId = status.MetaMsgId
		}

		webhookContact := &adapter_types.WebhookContact{
			Id: status.RecipientId,
		}

		var ackValue string
		switch status.Status {
		case "enqueued": // Usado na gupshup
			ackValue = "0"
		case "sent":
			ackValue = "1"
		case "delivered":
			ackValue = "2"
		case "read":
			ackValue = "3"
		// deleted não é suportado pela meta atualmente
		// https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples#status--message-deleted
		case "deleted":
			ackValue = "-1"
		case "failed":
			ackValue = "error"
		default:
			return nil, fmt.Errorf("unknown status type: %s", status.Status)
		}

		timestampDate := time.Now()

		parsedTime, err := cast.ToStringE(status.Timestamp)

		if err != nil {
			return nil, fmt.Errorf("failed to parse message timestamp: %w", err)
		}

		if parsedTime != "" {
			var milli int64

			if len(parsedTime) == 13 {
				milli = cast.ToInt64(parsedTime[10:13])
			}

			timestampDate = time.Unix(cast.ToInt64(parsedTime[:10]), milli)
		}

		webhookStatuses := &adapter_types.WebhookStatuses{
			Id:         statusId,
			Ack:        ackValue,
			Timestamp:  &timestampDate,
			WhatsappId: whatsappId, // Usado apenas na gupshup
		}

		if ackValue == "error" && status.Errors != nil && len(status.Errors) > 0 && status.Errors[0].Message != "" {
			webhookStatuses.Error = &models.MessageError{
				Error:   status.Errors[0].Title,
				Code:    status.Errors[0].Code,
				Message: status.Errors[0].Message,
			}

			originalError, error := json.Marshal(status.Errors)

			if error != nil {
				return nil, fmt.Errorf("failed to marshal original error: %w", error)
			}

			webhookStatuses.Error.OriginalError = string(originalError)
		}

		builtWebhooks = append(builtWebhooks, &adapter_types.BuiltWebhook{
			Contact:  webhookContact,
			From:     webhookContact,
			Statuses: webhookStatuses,
		})
	}

	return builtWebhooks, nil
}

func (a *BaseWabaAdapter) GetTemplates(ctx context.Context, serviceId uuid.UUID) ([]*models.WhatsappBusinessTemplate, error) {
	slog.InfoContext(ctx, "GetTemplates called", slog.String("serviceId", serviceId.String()))

	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	templateApiUrl, err := a.GetTemplatesApiUrl(ctx, service)
	if err != nil {
		return nil, fmt.Errorf("failed to get template api url: %w", err)
	}

	req, err := http.NewRequest("GET", templateApiUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	for key, value := range a.MessageApiHeaders {
		req.Header.Add(key, value)
	}

	res, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to get templates: %d", res.StatusCode)
	}

	slog.DebugContext(ctx, "Meta/360Dialog templates response", slog.String("body", string(body)))

	// Usar a estrutura definida em meta_types.go para a resposta
	var metaResponse *MetaTemplateResponse

	err = json.Unmarshal(body, &metaResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	// Determinar quais templates processar com base no formato da resposta
	var templatesData []*MetaTemplate
	if len(metaResponse.Data) > 0 {
		// Formato Meta padrão
		templatesData = metaResponse.Data
	} else if len(metaResponse.WabaTemplates) > 0 {
		// Formato 360Dialog
		templatesData = metaResponse.WabaTemplates
	} else {
		// Nenhum template encontrado
		return []*models.WhatsappBusinessTemplate{}, nil
	}

	var templates []*models.WhatsappBusinessTemplate
	for _, template := range templatesData {
		// Processar o template para o formato interno
		whatsappTemplate := a.processMetaTemplate(ctx, template, service, serviceId)
		templates = append(templates, whatsappTemplate)
	}

	return templates, nil
}

// processMetaTemplate converte um template da Meta para o formato interno
func (a *BaseWabaAdapter) processMetaTemplate(ctx context.Context, template *MetaTemplate, service *models.Service, serviceId uuid.UUID) *models.WhatsappBusinessTemplate {
	// Verificar se é um template de autenticação
	isAuth := strings.ToUpper(template.Category) == "AUTHENTICATION"

	// Converter componentes para o formato interno
	var components []*models.WhatsappBusinessComponent

	// Se for um template de autenticação, usar o processamento específico
	if isAuth {
		// Criar um template temporário para passar para a função de processamento
		tempTemplate := &models.WhatsappBusinessTemplate{
			Language: template.Language,
			Category: template.Category,
		}
		// Usar a função ProcessAuthenticationTemplate
		components = a.ProcessAuthenticationTemplate(ctx, tempTemplate, components)
	} else {
		// Processamento normal para templates não-autenticação
		for _, comp := range template.Components {
			component := &models.WhatsappBusinessComponent{
				Type:   comp.Type,
				Format: comp.Format,
				Text:   comp.Text,
			}

			// Processar exemplos se existirem
			if comp.Example != nil {
				if comp.Type == adapter_types.ComponentTypeHeader && comp.Format != adapter_types.ComponentFormatText {
					// Para componentes de cabeçalho com mídia, processar o exemplo
					headerHandle := comp.Example.HeaderText
					if len(comp.Example.HeaderHandle) > 0 {
						headerHandle = comp.Example.HeaderHandle
					}

					if len(headerHandle) > 0 {
						component.Example = &models.WhatsappBusinessComponentExample{
							HeaderHandle: headerHandle,
						}
					}
				} else if comp.Type == adapter_types.ComponentTypeBody && len(comp.Example.BodyText) > 0 {
					// Para componentes de corpo, processar o exemplo
					component.Example = &models.WhatsappBusinessComponentExample{
						BodyText: comp.Example.BodyText,
					}
				}
			}

			// Processar botões se existirem
			if len(comp.Buttons) > 0 {
				var buttons []*models.WhatsappBusinessComponentParameterButton
				for _, btn := range comp.Buttons {
					button := &models.WhatsappBusinessComponentParameterButton{
						Type:        models.WhatsappBusinessButtonTypeEnum(btn.Type),
						Text:        btn.Text,
						URL:         btn.URL,
						PhoneNumber: btn.PhoneNumber,
					}
					buttons = append(buttons, button)
				}
				component.Buttons = buttons
			}

			components = append(components, component)
		}
	}

	// Determinar o tipo de mensagem
	var messageType = models.WhatsappBusinessMessageTypeTextOnly

	// Verificar se tem botões para determinar se é interativo
	for _, comp := range components {
		if comp.Type == adapter_types.ComponentTypeButtons && len(comp.Buttons) > 0 || comp.Type == adapter_types.ComponentTypeHeader || comp.Type == adapter_types.ComponentTypeFooter {
			messageType = models.WhatsappBusinessMessageTypeInteractive
			break
		}
	}

	// Processar a qualidade do template
	var quality models.WhatsappBusinessTemplateQualityEnum
	if template.QualityScore != nil && template.QualityScore.Score != "" {
		quality = a.parseQuality(template.QualityScore.Score)
	} else {
		quality = a.parseQuality(template.Quality)
	}

	// Determinar o namespace
	namespace := template.Id
	if template.Namespace != "" {
		namespace = template.Namespace
	}

	// Determinar o status de rejeição
	rejectedReason := template.RejectedReason
	if rejectedReason == "NONE" {
		rejectedReason = ""
	}

	// Converter o status para a enumeração
	status := ParseStatus(template.Status)

	// Criar o template
	whatsappTemplate := &models.WhatsappBusinessTemplate{
		Name:           template.Name,
		Language:       template.Language,
		Status:         status,
		Category:       template.Category,
		Components:     components,
		MessageType:    messageType,
		Namespace:      namespace,
		ServiceId:      serviceId,
		AccountId:      service.AccountId,
		Quality:        quality,
		RejectedReason: rejectedReason,
	}

	return whatsappTemplate
}

// parseQuality converte a qualidade do template do formato Meta para o formato interno
func (a *BaseWabaAdapter) parseQuality(quality string) models.WhatsappBusinessTemplateQualityEnum {
	switch quality {
	case "GREEN", "HIGH":
		return models.WhatsappBusinessTemplateQualityHigh
	case "YELLOW", "MEDIUM":
		return models.WhatsappBusinessTemplateQualityMedium
	case "RED", "LOW":
		return models.WhatsappBusinessTemplateQualityLow
	default:
		return models.WhatsappBusinessTemplateQualityUnknown
	}
}

// ParseStatus converte o status do template de string para a enumeração WhatsappBusinessTemplateStatusEnum
func ParseStatus(status string) models.WhatsappBusinessTemplateStatusEnum {
	switch strings.ToUpper(status) {
	case "APPROVED":
		return models.WhatsappBusinessTemplateStatusApproved
	case "PENDING":
		return models.WhatsappBusinessTemplateStatusPending
	case "REJECTED":
		return models.WhatsappBusinessTemplateStatusRejected
	case "FAILED":
		return models.WhatsappBusinessTemplateStatusFailed
	case "SENDING":
		return models.WhatsappBusinessTemplateStatusSending
	default:
		return models.WhatsappBusinessTemplateStatusEmpty
	}
}

// CreateTemplate cria um template no Meta WhatsApp Business API
func (a *BaseWabaAdapter) CreateTemplate(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (*models.WhatsappBusinessTemplate, error) {
	slog.InfoContext(ctx, "CreateTemplate called", slog.String("serviceId", serviceId.String()), slog.String("templateId", templateId.String()))

	if templateId == uuid.Nil {
		return nil, fmt.Errorf("templateId is required to create a new WABA template")
	}

	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	template, err := a.templateRepository.FindById(ctx, templateId)
	if err != nil {
		return nil, fmt.Errorf("failed to find template with id %s: %w", templateId, err)
	}

	if template.ArchivedAt != nil {
		return nil, fmt.Errorf("template is archived")
	}

	// Preparar o payload para a API do Meta
	requestPayload, err := a.prepareTemplateRequestPayload(ctx, service, template)

	if err != nil {
		return nil, fmt.Errorf("failed to prepare template request payload: %w", err)
	}

	jsonData, err := json.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal template payload: %w", err)
	}

	apiUrl, err := a.GetTemplatesApiUrl(ctx, service)
	if err != nil {
		return nil, fmt.Errorf("failed to get template api url: %w", err)
	}

	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create HTTP request",
			slog.String("apiUrl", apiUrl),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	headers, err := a.GetMessageApiHeaders(ctx, service)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get message API headers",
			slog.String("serviceId", serviceId.String()),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get message api headers: %w", err)
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	req.Header.Set("Content-Type", "application/json")

	// Enviar a requisição
	slog.DebugContext(ctx, "Sending template creation request",
		slog.String("apiUrl", apiUrl),
		slog.String("templateId", templateId.String()),
		slog.String("payload", string(jsonData)))

	res, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode == 400 {
		rejectedReason := a.extractRejectedReasonFromResponse(body)
		template.Status = models.WhatsappBusinessTemplateStatusRejected
		template.RejectedReason = rejectedReason

		return template, nil
	}

	if res.StatusCode != 200 && res.StatusCode != 201 {
		return nil, fmt.Errorf("failed to create template: status code %d, response: %s", res.StatusCode, string(body))
	}

	var responseData *MetaCreateTemplateResponse

	err = json.Unmarshal(body, &responseData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	slog.InfoContext(ctx, "Template created successfully",
		slog.String("templateId", templateId.String()),
		slog.String("status", string(responseData.Status)),
		slog.String("id", responseData.Id))

	template.Status = ParseStatus(string(responseData.Status))

	if responseData.Namespace != "" {
		template.Namespace = responseData.Namespace
	}

	if responseData.Id != "" {
		template.IdGupshup = responseData.Id
	}

	err = a.templateRepository.UpdateById(ctx, template.Id, template)
	if err != nil {
		return nil, fmt.Errorf("failed to update template after creation: %w", err)
	}

	return template, nil
}

// prepareTemplateRequestPayload prepara o payload para a criação de template
func (a *BaseWabaAdapter) prepareTemplateRequestPayload(ctx context.Context, service *models.Service, template *models.WhatsappBusinessTemplate) (*MetaCreateTemplateRequest, error) {
	payload := &MetaCreateTemplateRequest{
		Name:                template.Name,
		Language:            template.Language,
		Category:            template.Category,
		AllowCategoryChange: true,
		Components:          []*MetaComponentRequest{},
	}

	isAuthTemplate := strings.ToUpper(template.Category) == "AUTHENTICATION"

	for _, comp := range template.Components {
		component := &MetaComponentRequest{
			Type: comp.Type,
		}

		if isAuthTemplate {
			// Não adicionar text!
			if comp.Type == adapter_types.ComponentTypeBody {
				if comp.AddSecurityRecommendation {
					component.AddSecurityRecommendation = true
				}
			}

			if comp.Type == adapter_types.ComponentTypeFooter && comp.ActiveCodeExpirationMinutes {
				component.CodeExpirationMinutes = comp.CodeExpirationMinutes
			}

			if comp.Type == adapter_types.ComponentTypeButtons {
				component.Type = adapter_types.ComponentTypeButtons

				if comp.Buttons[0].Type == models.WhatsappBusinessButtonTypeOTP {
					button := &MetaButtonRequest{
						Type:    MetaButtonTypeRequestEnumOTP,
						Text:    comp.Buttons[0].Text,
						OtpType: MetaOTPType(comp.Buttons[0].OtpType),
					}

					switch comp.Buttons[0].OtpType {
					case models.WhatsappBusinessButtonOtpTypeCopyCode:
						// Não precisa de campos adicionais
						break
					case models.WhatsappBusinessButtonOtpTypeOneTap:
						// Adicionar campos para ONE_TAP
						button.AutofillText = comp.Buttons[0].AutofillText
						button.PackageName = comp.Buttons[0].PackageName
						button.SignatureHash = comp.Buttons[0].SignatureHash
					case models.WhatsappBusinessButtonOtpTypeZeroTap:
						// Adicionar campos para ZERO_TAP (Não temos ainda no front da digisac, mas da pra ser sincronizado se criado pelo provedor)
						button.AutofillText = comp.Buttons[0].AutofillText
						button.PackageName = comp.Buttons[0].PackageName
						button.SignatureHash = comp.Buttons[0].SignatureHash
						button.ZeroTapTermsAccepted = true // Sempre true para ZERO_TAP
					}

					component.Buttons = []*MetaButtonRequest{button}
				}
			}
			payload.Components = append(payload.Components, component)
			continue
		}

		if comp.Text != "" {
			component.Text = comp.Text
		}

		if comp.Format != "" {
			component.Format = comp.Format
		}

		if len(comp.Buttons) > 0 {
			component.Buttons = []*MetaButtonRequest{}
			for _, btn := range comp.Buttons {
				button := &MetaButtonRequest{
					Type: MetaButtonTypeRequestEnum(btn.Type),
					Text: btn.Text,
				}

				if btn.Type == "URL" && btn.URL != "" {
					button.URL = btn.URL
				}

				if btn.Type == "PHONE_NUMBER" && btn.PhoneNumber != "" {
					button.PhoneNumber = btn.PhoneNumber
				}

				component.Buttons = append(component.Buttons, button)
			}
		}

		if comp.Example != nil {
			example := &MetaExampleRequest{}

			if comp.Type == adapter_types.ComponentTypeHeader {
				url, err := a.GetMediaExampleUrl(ctx, template.ServiceId, template.Id)

				if err != nil {
					return nil, fmt.Errorf("failed to get media example url: %w", err)
				}

				if service.Data.ProviderType == "meta" {
					example.HeaderHandle = append(example.HeaderHandle, url)
				} else {
					example.HeaderHandle = append(example.HeaderHandle, url)
				}
			}

			if len(comp.Example.HeaderText) > 0 && comp.Type == adapter_types.ComponentTypeHeader && comp.Format == adapter_types.ComponentFormatText {
				example.HeaderText = comp.Example.HeaderText
			}

			if len(comp.Example.BodyText) > 0 && comp.Type == adapter_types.ComponentTypeBody {
				example.BodyText = comp.Example.BodyText
			}

			if len(example.HeaderHandle) > 0 || len(example.HeaderText) > 0 || len(example.BodyText) > 0 {
				component.Example = example
			}
		}

		// Verificar se o componente tem conteúdo válido antes de adicionar
		if component.Text != "" || component.Format != "" || component.Example != nil || len(component.Buttons) > 0 {
			payload.Components = append(payload.Components, component)
		}
	}

	// Adicionar log do payload para depuração
	payloadJSON, _ := json.Marshal(payload)
	slog.DebugContext(ctx, "Template request payload", slog.String("payload", string(payloadJSON)))

	return payload, nil
}

// extractRejectedReasonFromResponse extrai o motivo da rejeição de uma resposta de erro
func (a *BaseWabaAdapter) extractRejectedReasonFromResponse(responseBody []byte) string {
	rejectedReason := string(responseBody)

	var templateError TemplateErrorResponse
	if err := json.Unmarshal(responseBody, &templateError); err == nil && templateError.Meta != nil {
		if templateError.Meta.Error != "" {
			var fbError *FacebookErrorResponse
			if err := json.Unmarshal([]byte(templateError.Meta.Error), &fbError); err == nil {
				if fbError.Error != nil {
					if fbError.Error.ErrorUserMsg != "" {
						return fbError.Error.ErrorUserMsg
					} else if fbError.Error.Message != "" {
						return fbError.Error.Message
					}
				}
			}
			return templateError.Meta.Error
		} else if templateError.Meta.DeveloperMessage != "" {
			return templateError.Meta.DeveloperMessage
		}
	}

	return rejectedReason
}

// DeleteTemplate deleta um template no Meta WhatsApp Business API
func (a *BaseWabaAdapter) DeleteTemplate(ctx context.Context, serviceId uuid.UUID, templateName string) error {
	slog.InfoContext(ctx, "DeleteTemplate called", slog.String("serviceId", serviceId.String()), slog.String("templateName", templateName))

	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	apiUrl, err := a.DeleteTemplateApiUrl(ctx, service, templateName)
	if err != nil {
		return fmt.Errorf("failed to get delete template api url: %w", err)
	}

	req, err := http.NewRequest("DELETE", apiUrl, nil)
	if err != nil {
		return fmt.Errorf("failed to create delete request: %w", err)
	}

	headers, err := a.DeleteTemplateApiHeaders(ctx, service)
	if err != nil {
		return fmt.Errorf("failed to get message api headers: %w", err)
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	res, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send delete request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 && res.StatusCode != 204 {
		return fmt.Errorf("failed to delete template: status code %d, response: %s", res.StatusCode, string(body))
	}

	return nil
}

func (a *BaseWabaAdapter) SendVCards(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendVCardsPayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}
