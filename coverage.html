
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>vcard: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">digisac-go/worker/core/utils/vcard/get_contact_from_vcard.go (100.0%)</option>
				
				<option value="file1">digisac-go/worker/core/utils/vcard/get_vcard.go (100.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package vcard

import (
        "fmt"
        "strings"
)

func GetContactFromVCard(vcard string) (*VCardContact, error) <span class="cov8" title="1">{
        if vcard == "" </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("vcard is empty")
        }</span>

        <span class="cov8" title="1">lines := strings.Split(vcard, "\n")
        contact := &amp;VCardContact{
                Name:   &amp;ContactName{},
                Phones: []*ContactPhones{},
        }

        for _, line := range lines </span><span class="cov8" title="1">{
                line = strings.TrimSpace(line)

                if strings.HasPrefix(line, "N:") </span><span class="cov8" title="1">{
                        // Ex: N:Silva;João;;;
                        parts := strings.Split(line[2:], ";")
                        if len(parts) &gt;= 2 </span><span class="cov8" title="1">{
                                contact.Name.LastName = strings.TrimSpace(parts[0])
                                contact.Name.FirstName = strings.TrimSpace(parts[1])
                        }</span>
                } else<span class="cov8" title="1"> if strings.HasPrefix(line, "FN:") </span><span class="cov8" title="1">{
                        // Ex: FN:João Silva (opcional, já usamos N para isso)
                        continue</span>
                } else<span class="cov8" title="1"> if strings.HasPrefix(line, "item1.TEL;waid=") </span><span class="cov8" title="1">{
                        // Ex: item1.TEL;waid=5511999999999:5511999999999
                        waidSection := strings.TrimPrefix(line, "item1.TEL;waid=")
                        parts := strings.SplitN(waidSection, ":", 2)
                        if len(parts) == 2 </span><span class="cov8" title="1">{
                                phone := &amp;ContactPhones{
                                        WaId:  strings.TrimSpace(parts[0]),
                                        Phone: strings.TrimSpace(parts[1]),
                                        Type:  "Mobile",
                                }
                                contact.Phones = append(contact.Phones, phone)
                        }</span>
                }
        }

        <span class="cov8" title="1">if contact.Name.FirstName == "" &amp;&amp; contact.Name.LastName == "" &amp;&amp; len(contact.Phones) == 0 </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("vcard content is invalid or incomplete")
        }</span>

        <span class="cov8" title="1">return contact, nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package vcard

import (
        "fmt"
        "strings"
)

type ContactName struct {
        FirstName string `json:"first_name"`
        LastName  string `json:"last_name"`
}

type ContactPhones struct {
        Phone string `json:"phone"`
        Type  string `json:"type"`
        WaId  string `json:"wa_id"`
}

type VCardContact struct {
        Name   *ContactName     `json:"name"`
        Phones []*ContactPhones `json:"phones"`
}

func GetVCard(contact *VCardContact) (string, error) <span class="cov8" title="1">{
        if contact == nil </span><span class="cov8" title="1">{
                return "", fmt.Errorf("contact is nil")
        }</span>

        <span class="cov8" title="1">var firstName, lastName string

        if contact.Name != nil </span><span class="cov8" title="1">{
                firstName = contact.Name.FirstName
                lastName = contact.Name.LastName
        }</span>

        <span class="cov8" title="1">var phone, waId, phoneNumber string
        if len(contact.Phones) &gt; 0 </span><span class="cov8" title="1">{
                firstPhone := contact.Phones[0]

                if firstPhone.WaId != "" </span><span class="cov8" title="1">{
                        phone = firstPhone.WaId
                }</span> else<span class="cov8" title="1"> {
                        phone = cleanNumber(firstPhone.Phone)
                }</span>
                <span class="cov8" title="1">waId = phone

                phoneNumber = cleanNumber(firstPhone.Phone)
                if phoneNumber == "" </span><span class="cov8" title="1">{
                        phoneNumber = firstPhone.WaId
                }</span>
        }

        <span class="cov8" title="1">return fmt.Sprintf(
                "BEGIN:VCARD\nVERSION:3.0\nN:%s;%s;;;\nFN:%s %s\nitem1.TEL;waid=%s:%s\nitem1.X-ABLabel:Mobile\nEND:VCARD",
                lastName, firstName, firstName, lastName, waId, phoneNumber,
        ), nil</span>
}

func cleanNumber(number string) string <span class="cov8" title="1">{
        return strings.Map(func(r rune) rune </span><span class="cov8" title="1">{
                if r &gt;= '0' &amp;&amp; r &lt;= '9' </span><span class="cov8" title="1">{
                        return r
                }</span>
                <span class="cov8" title="1">return -1</span>
        }, number)
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
